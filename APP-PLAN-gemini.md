# Plan: Go "Hello World" Desktop App (Docker Build, AppImage Output)

This plan outlines the steps to create a "Hello World" GUI application in Go, compile it within a Docker container, and package it as an AppImage. No local Go installation is required.

## Phase 1: Project Setup and Application Code

### Step 1: Create Project Directory Structure

Create the following directory structure for your project:

```
my-go-app/
├── main.go              # Go application source code
├── go.mod               # Go module definition
├── Dockerfile           # Docker instructions for building and packaging
├── my-go-app.desktop    # Desktop entry file for AppImage
├── AppRun               # Script to run the application within AppImage
├── my-go-app.png        # Application icon (e.g., 256x256 PNG)
└── scripts/
    └── build.sh         # Build script to automate Docker and AppImage creation
```

- You will need to create `my-go-app.png`. A simple 256x256 PNG image will suffice.

### Step 2: Initialize Go Module

Create a `go.mod` file in the project root (`my-go-app/go.mod`) with the following content:

```go
module my-go-app

go 1.21 // Or a newer stable version

require fyne.io/fyne/v2 v2.4.0 // Example version, Docker build will update/verify
```

_(Note: `go mod tidy` will be run inside Docker to finalize dependencies based on `main.go`)_

### Step 3: Write the "Hello, World!" Go Desktop Application

Create `my-go-app/main.go` with the following content. We'll use the Fyne toolkit for the GUI.

```go
package main

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

func main() {
	myApp := app.New()
	myWindow := myApp.NewWindow("Hello Go App")

	hello := widget.NewLabel("Hello, World from Go!")
	myWindow.SetContent(container.NewCenter(hello))
	myWindow.Resize(fyne.NewSize(300, 150)) // Set an initial window size
	myWindow.ShowAndRun()
}
```

## Phase 2: Dockerization for Compilation and Packaging

### Step 4: Create the `Dockerfile`

This Dockerfile will have two stages:

1.  `builder`: Compiles the Go application.
2.  `appimage_creator`: Takes the compiled binary and packages it into an AppImage.

Create `my-go-app/Dockerfile` with the following content:

```dockerfile
# Stage 1: Builder
FROM golang:1.21-alpine AS builder

ARG APP_NAME=my-go-app

# Install Fyne dependencies for Alpine (CGO, GTK, etc.)
RUN apk add --no-cache build-base gcc musl-dev mesa-dev libx11-dev libxi-dev libxcursor-dev libxrandr-dev libxinerama-dev libgl1-mesa-glx libglu1-mesa glfw-dev gtk+3.0-dev adwaita-icon-theme

WORKDIR /app

COPY go.mod go.sum* ./
# go.sum might not exist initially or be incomplete.
# `go mod download` fetches dependencies listed in go.mod.
# `go mod tidy` ensures go.mod matches source code, removes unused, adds missing, and updates go.sum.
RUN go mod download && go mod tidy

COPY . .

# Build the Go application
# Static linking is preferred for portability if possible, but Fyne usually links dynamically to system GTK.
# Alpine/musl helps reduce glibc dependencies.
RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o /out/${APP_NAME} main.go

# Stage 2: AppImage Creator
FROM ubuntu:22.04 AS appimage_creator

ARG APP_NAME=my-go-app
ARG APP_VERSION=0.1.0

# Install appimagetool dependencies (fuse is for running AppImages, not strictly for creation tool)
# wget for downloading, libgtk-3-0 might be needed by some bundled apps if not present, or by tools.
RUN apt-get update && apt-get install -y wget fuse ca-certificates libgtk-3-0 && rm -rf /var/lib/apt/lists/*

# Download appimagetool
RUN wget https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage -O /usr/local/bin/appimagetool && \
    chmod +x /usr/local/bin/appimagetool

WORKDIR /build_appimage

# Create AppDir structure
# Standard locations for icons and .desktop files.
RUN mkdir -p AppDir/usr/bin AppDir/usr/share/icons/hicolor/256x256/apps AppDir/usr/share/applications
RUN mkdir -p AppDir/output # Directory to store the final AppImage

# Copy the compiled binary from the builder stage
COPY --from=builder /out/${APP_NAME} AppDir/usr/bin/

# Copy AppRun script (expected in Docker build context root)
COPY AppRun AppDir/
RUN chmod +x AppDir/AppRun

# Copy .desktop file (expected in context root, named ${APP_NAME}.desktop)
COPY ${APP_NAME}.desktop AppDir/
COPY ${APP_NAME}.desktop AppDir/usr/share/applications/${APP_NAME}.desktop

# Copy icon (expected in context root as ${APP_NAME}.png)
# It's copied to AppDir root (for direct reference by AppRun or .desktop if needed)
# and to a standard icon path.
COPY ${APP_NAME}.png AppDir/
COPY ${APP_NAME}.png AppDir/usr/share/icons/hicolor/256x256/apps/${APP_NAME}.png

# Set executable bit on the main binary
RUN chmod +x AppDir/usr/bin/${APP_NAME}

# Generate the AppImage
ENV ARCH=x86_64
RUN cd AppDir && /usr/local/bin/appimagetool . ../output/${APP_NAME}-${APP_VERSION}-${ARCH}.AppImage
# Note: Running appimagetool from within the AppDir parent and specifying AppDir as source.
# Outputting to a subdirectory to keep things clean.
```

### Step 5: Create `AppRun` Script

This script is the entry point for the AppImage. Create `my-go-app/AppRun`:

```bash
#!/bin/bash

# Get the directory where AppRun is located (the root of the AppImage filesystem)
HERE="$(dirname "$(readlink -f "${0}")")"

# Optional: Set up environment variables if needed
# export LD_LIBRARY_PATH="${HERE}/usr/lib:${LD_LIBRARY_PATH}"
# export PATH="${HERE}/usr/bin:${PATH}"
# (Fyne apps built on Alpine/musl are often quite self-contained regarding libc,
# but GTK dependencies might need bundling for wider compatibility.
# This basic AppRun does not bundle/redirect libraries yet.)

# Execute the main application binary
# The binary is expected to be in usr/bin relative to AppRun.
exec "${HERE}/usr/bin/my-go-app" "$@"
```

_Ensure this file is executable (`chmod +x AppRun`) before building, though the Dockerfile also does this._

### Step 6: Create `.desktop` File

This file provides metadata for the application. Create `my-go-app/my-go-app.desktop`:

```ini
[Desktop Entry]
Version=1.0
Name=My Go App
Comment=A simple Go Hello World desktop application
Exec=my-go-app
Icon=my-go-app
Terminal=false
Type=Application
Categories=Utility;Development;
```

_(Note: `Exec` should be the name of your binary. `Icon` should be the name of your icon file without the extension, assuming it's placed where the system or AppImage tools can find it, like the root of the AppDir or standard icon paths within.)_

## Phase 3: Build Automation

### Step 7: Create Build Script (Optional but Recommended)

This script automates the Docker build and AppImage extraction. Create `my-go-app/scripts/build.sh`:

```bash
#!/bin/bash
set -e # Exit immediately if a command exits with a non-zero status.

# --- Configuration ---
APP_NAME="my-go-app"
APP_VERSION="0.1.0"
DOCKER_IMAGE_NAME="golang-desktop-builder" # Name for your Docker build image
OUTPUT_DIR_HOST="./dist"                   # Output directory on your host machine

# --- Pre-flight checks (ensure essential files exist in project root) ---
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)" # Get project root relative to script

REQUIRED_FILES=(
    "${PROJECT_ROOT}/main.go"
    "${PROJECT_ROOT}/go.mod"
    "${PROJECT_ROOT}/Dockerfile"
    "${PROJECT_ROOT}/${APP_NAME}.desktop"
    "${PROJECT_ROOT}/AppRun"
    "${PROJECT_ROOT}/${APP_NAME}.png"
)

for f in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$f" ]; then
        echo "Error: Required file not found: $f"
        exit 1
    fi
done
echo "All required files found."

# --- Docker Build ---
echo ""
echo "Building Docker image: $DOCKER_IMAGE_NAME (using Dockerfile in $PROJECT_ROOT)..."
docker build \
    --build-arg APP_NAME="$APP_NAME" \
    --build-arg APP_VERSION="$APP_VERSION" \
    -t "$DOCKER_IMAGE_NAME" -f "${PROJECT_ROOT}/Dockerfile" "$PROJECT_ROOT"

# --- AppImage Extraction ---
echo ""
echo "Creating output directory on host: $OUTPUT_DIR_HOST"
mkdir -p "${PROJECT_ROOT}/${OUTPUT_DIR_HOST}"

echo "Creating temporary container to extract AppImage..."
# The final AppImage is created in /build_appimage/output/ inside the container.
APPIMAGE_FILENAME_IN_CONTAINER="/build_appimage/output/${APP_NAME}-${APP_VERSION}-x86_64.AppImage"
CONTAINER_NAME="temp_${APP_NAME}_builder_$$" # Unique container name

# Create container (does not start it)
docker create --name "$CONTAINER_NAME" "$DOCKER_IMAGE_NAME"

FINAL_APPIMAGE_HOST_PATH="${PROJECT_ROOT}/${OUTPUT_DIR_HOST}/${APP_NAME}-${APP_VERSION}-linux-x86_64.AppImage"

echo "Extracting AppImage: $APPIMAGE_FILENAME_IN_CONTAINER to $FINAL_APPIMAGE_HOST_PATH"
docker cp "${CONTAINER_NAME}:${APPIMAGE_FILENAME_IN_CONTAINER}" "$FINAL_APPIMAGE_HOST_PATH"

echo "Cleaning up temporary container..."
docker rm -f "$CONTAINER_NAME"

# --- Post Build ---
echo ""
echo "--------------------------------------------------"
echo "AppImage created: $FINAL_APPIMAGE_HOST_PATH"
echo "--------------------------------------------------"
echo "Next steps:"
echo "1. Make the AppImage executable: chmod +x \"$FINAL_APPIMAGE_HOST_PATH\""
echo "2. Run it on a Linux system: \"$FINAL_APPIMAGE_HOST_PATH\""
echo "--------------------------------------------------"
```

_Make this script executable: `chmod +x scripts/build.sh`_

## Phase 4: Building and Running

### Step 8: Build the Application

1.  Ensure you have Docker installed and running.
2.  Navigate to the `my-go-app/scripts/` directory in your terminal.
3.  Run the build script: `./build.sh`

This will:

- Build the Docker image.
- Compile your Go application inside Docker.
- Package it into an AppImage using `appimagetool` inside Docker.
- Copy the final `.AppImage` file to the `my-go-app/dist/` directory on your host machine.

### Step 9: Run the AppImage

1.  Navigate to the `my-go-app/dist/` directory.
2.  Make the AppImage executable: `chmod +x my-go-app-0.1.0-linux-x86_64.AppImage` (the exact name might vary slightly if you change `APP_NAME` or `APP_VERSION`).
3.  Run the application: `./my-go-app-0.1.0-linux-x86_64.AppImage`

## Phase 5: Considerations and Future Enhancements

### Dependency Bundling for GUI

- The current setup relies on Fyne building a relatively portable binary, especially when compiled on Alpine (musl).
- If you encounter runtime errors on different Linux distributions due to missing shared libraries (e.g., specific versions of GTK or other graphical libraries), you may need to enhance the AppImage bundling process.
- Tools like `linuxdeploy` (with plugins like `linuxdeploy-plugin-gtk`) can help bundle necessary dependencies. This would involve modifying the `appimage_creator` stage in your `Dockerfile` to use `linuxdeploy` instead of or in addition to `appimagetool` directly for creating the `AppDir`.

### Icon

- Ensure `my-go-app.png` is a valid PNG image. A 256x256 pixel image is a good size for `usr/share/icons/hicolor/256x256/apps`.

### Cross-Compilation (Other Binaries)

- The current `Dockerfile` is set up for Linux (`GOOS=linux`).
- To compile for other operating systems (e.g., Windows, macOS), you would need to:
  1.  Modify the `GOOS` and `GOARCH` environment variables in the `go build` command.
  2.  Install appropriate cross-compilers in the Docker build stage (e.g., MinGW for Windows).
  3.  Use different packaging methods suitable for those platforms (e.g., MSI installers for Windows, `.app` bundles for macOS).
  - This would likely involve separate Docker stages or different Dockerfiles tailored for each target.

This plan provides a solid foundation for building your Go desktop application with Docker and packaging it as an AppImage.
