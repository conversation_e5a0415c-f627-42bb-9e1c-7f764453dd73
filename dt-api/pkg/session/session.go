package session

import (
	"context"
	"time"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
)

type Session struct {
	ID        string
	UserID    uuid.UUID
	ExpiresAt time.Time
}

type SessionStore struct {
	client *redis.Client
}

func NewSessionStore(redisURL string) (*SessionStore, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, err
	}

	client := redis.NewClient(opt)

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, err
	}

	return &SessionStore{client: client}, nil
}

func (s *SessionStore) CreateSession(userID uuid.UUID, duration time.Duration) (*Session, error) {
	session := &Session{
		ID:        uuid.New().String(),
		UserID:    userID,
		ExpiresAt: time.Now().Add(duration),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := s.client.Set(ctx, session.ID, userID.String(), duration).Err()
	if err != nil {
		return nil, err
	}

	return session, nil
}

func (s *SessionStore) GetSession(sessionID string) (*Session, error) {
	userIDStr, err := s.client.Get(context.Background(), sessionID).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, err
	}

	return &Session{
		ID:     sessionID,
		UserID: userID,
	}, nil
}

func (s *SessionStore) DeleteSession(sessionID string) error {
	return s.client.Del(context.Background(), sessionID).Err()
} 