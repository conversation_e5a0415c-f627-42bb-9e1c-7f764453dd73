package middleware

import (
	"Api/pkg/session"
	"net/http"

	"github.com/gin-gonic/gin"
)

func SessionMiddleware(store *session.SessionStore) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get session token from cookie or header
		sessionID := c.<PERSON>("X-Session-Token")
		if sessionID == "" {
			cookie, err := c.<PERSON>("session_token")
			if err == nil {
				sessionID = cookie
			}
		}

		if sessionID == "" {
			c.Next()
			return
		}

		// Validate session
		sess, err := store.GetSession(sessionID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Session error"})
			c.Abort()
			return
		}

		if sess == nil {
			c.Next()
			return
		}

		// Store session in context
		c.Set("session", sess)
		c.Next()
	}
} 