services:
  api:
    build: .
    ports:
      - "${API_PORT}:${API_PORT}"
    environment:
      - JWT_PRIVATE_KEY_PATH=${JWT_PRIVATE_KEY_PATH}
      - JWT_PUBLIC_KEY_PATH=${JWT_PUBLIC_KEY_PATH}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - API_PORT=${API_PORT}
      - API_PREFIX=${API_PREFIX}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_USER=${REDIS_USER}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=${REDIS_DB}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      # Mount only what's needed for development
      - ./keys:/app/keys:ro
      - ./db/migrations:/app/db/migrations:ro
    networks:
      - app-network

  mysql:
    image: mysql:5.7
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
    ports:
      - "${DB_PORT}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./db/init:/docker-entrypoint-initdb.d
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 5s
      retries: 10
      interval: 5s

  redis:
    image: redis:alpine
    ports:
      - "${REDIS_PORT}:6379"
    command: redis-server ${REDIS_PASSWORD:+--requirepass ${REDIS_PASSWORD}}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data: 