package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type User struct {
	ID        uuid.UUID `json:"id" gorm:"type:char(36);primary_key"`
	Email     string    `json:"email" gorm:"unique;not null"`
	Pass      string    `json:"-" gorm:"column:pass;not null"`
	License   string    `json:"license"`
	Currency  int8      `json:"currency"`
	DtCreated time.Time `json:"createdAt" gorm:"column:dt_created;autoCreateTime"`
	DtUpdated time.Time `json:"updatedAt" gorm:"column:dt_updated;autoUpdateTime"`
	Jwt       string    `gorm:"type:text" json:"-"`
}

// Before<PERSON><PERSON> will set a UUID rather than numeric ID
func (user *User) BeforeCreate(tx *gorm.DB) error {
	if user.ID == uuid.Nil {
		user.ID = uuid.New()
	}
	return nil
} 