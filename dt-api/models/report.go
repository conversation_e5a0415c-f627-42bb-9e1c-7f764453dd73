package models

import (
	"database/sql"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"time"
)

type Report struct {
	ID            uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Uid           uuid.UUID      `gorm:"type:uuid;not null" json:"uid"`
	Year          int           `gorm:"type:int;not null" json:"year"`
	Month         int           `gorm:"type:int;not null" json:"month"`
	IsLocked      int8          `gorm:"type:tinyint;not null;default:0" json:"isLocked"`
	CurrencyRates sql.NullString `gorm:"type:text" json:"currencyRates"`
	Holdings      string        `gorm:"type:text;not null" json:"holdings"`
	DtCreated     time.Time     `json:"createdAt" gorm:"column:dt_created;autoCreateTime"`
	DtUpdated     time.Time     `json:"updatedAt" gorm:"column:dt_updated;autoUpdateTime"`

	// Foreign key relationship
	User User `gorm:"foreignKey:Uid" json:"-"`
}

// BeforeC<PERSON> will set a UUID rather than numeric ID
func (report *Report) BeforeCreate(tx *gorm.DB) error {
	if report.ID == uuid.Nil {
		report.ID = uuid.New()
	}
	return nil
} 