package models

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type Holding struct {
	ID                uuid.UUID       `json:"id" gorm:"type:char(36);primary_key"`
	Uid               uuid.UUID       `json:"uid" gorm:"type:char(36);not null"`
	Currency          string          `gorm:"type:varchar(10);not null" json:"currency"`
	Symbol            string          `gorm:"type:varchar(20);not null" json:"symbol"`
	Count             decimal.Decimal `gorm:"type:decimal(20,10);not null" json:"count"`
	Price             decimal.Decimal `gorm:"type:decimal(20,10);not null" json:"price"`
	Dividend          decimal.Decimal `gorm:"type:decimal(20,10);not null" json:"dividend"`
	DividendCurrency  int8            `gorm:"type:tinyint;not null" json:"dividendCurrency"`
	DividendFrequency int8            `gorm:"type:tinyint;not null" json:"dividendFrequency"`
	Position          int             `gorm:"type:int;not null" json:"position"`
	Name              string          `json:"name"`
	DtCreated         time.Time       `json:"createdAt" gorm:"column:dt_created;autoCreateTime"`
	DtUpdated         time.Time       `json:"updatedAt" gorm:"column:dt_updated;autoUpdateTime"`

	// Foreign key relationship
	User User `gorm:"foreignKey:Uid" json:"-"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (holding *Holding) BeforeCreate(tx *gorm.DB) error {
	if holding.ID == uuid.Nil {
		holding.ID = uuid.New()
	}
	return nil
} 