package models

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Goal struct {
	ID          uuid.UUID       `json:"id" gorm:"type:char(36);primary_key"`
	Uid         uuid.UUID       `json:"uid" gorm:"type:char(36);not null"`
	Dividends   decimal.Decimal `json:"dividends"`
	Networth    decimal.Decimal `json:"networth"`
	Currency    int8            `json:"currency"`

	// Foreign key relationship
	User User `gorm:"foreignKey:Uid" json:"-"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (goal *Goal) BeforeCreate(tx *gorm.DB) error {
	if goal.ID == uuid.Nil {
		goal.ID = uuid.New()
	}
	return nil
} 