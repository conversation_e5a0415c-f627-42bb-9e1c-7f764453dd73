CREATE TABLE IF NOT EXISTS currency_rates (
    id INT PRIMARY KEY,
    code VARCHAR(10) NOT NULL UNIQUE,
    rate DECIMAL(20,10) NOT NULL,
    dt_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert some default currency rates
INSERT INTO currency_rates (id, code, rate) VALUES
    (1, 'USD', 1.0000000000),
    (2, 'CAD', 1.3500000000),
    (3, 'RUB', 92.5000000000),
    (4, 'EUR', 0.9200000000),
    (5, 'GBP', 0.7900000000); 