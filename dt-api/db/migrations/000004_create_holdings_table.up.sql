CREATE TABLE IF NOT EXISTS holdings (
    id CHAR(36) PRIMARY KEY,
    uid CHAR(36) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    count DECIMAL(20,10) NOT NULL,
    price DECIMAL(20,10) NOT NULL,
    dividend DECIMAL(20,10) NOT NULL,
    dividend_currency TINYINT NOT NULL,
    dividend_frequency TINYINT NOT NULL,
    position INT NOT NULL,
    FOREIGN KEY (uid) REFERENCES users(id) ON DELETE CASCADE
); 