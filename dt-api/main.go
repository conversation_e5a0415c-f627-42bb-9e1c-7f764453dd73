package main

import (
	"Api/config"
	"Api/handlers"
	"Api/middleware"
	"Api/pkg/session"
	"fmt"
	"log"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func initDB(config *config.Config) *gorm.DB {
	db, err := gorm.Open(mysql.Open(config.GetDSN()), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	return db
}

func setupRouter(db *gorm.DB, cfg *config.Config) *gin.Engine {
	// Initialize Gin router
	router := gin.Default()

	// Configure CORS middleware
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"}, // Allow all origins
		AllowMethods:     []string{"GET", "POST", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{
			"Origin",
			"Content-Type",
			"Accept",
			"Authorization",
			"X-Request-Id",
			"X-Requested-With",
			"Access-Control-Request-Method",
			"Access-Control-Request-Headers",
		},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: false, // Must be false when using "*" for AllowOrigins
		MaxAge:           12 * time.Hour,
	}))

	// Initialize session store with error handling
	sessionStore, err := session.NewSessionStore(cfg.GetRedisURL())
	if err != nil {
		log.Printf("Warning: Failed to initialize Redis session store: %v", err)
		// Initialize with fallback configuration
		sessionStore, err = session.NewSessionStore("redis://redis:6379/0")
		if err != nil {
			log.Fatalf("Failed to initialize session store with fallback: %v", err)
		}
	}

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(db, sessionStore)
	userHandler := handlers.NewUserHandler(db)
	goalHandler := handlers.NewGoalHandler(db)
	holdingHandler := handlers.NewHoldingHandler(db)
	currencyRateHandler := handlers.NewCurrencyRateHandler(db)
	reportHandler := handlers.NewReportHandler(db)

	// Create API router group with version
	api := router.Group(cfg.APIPrefix)
	{
		// Public routes
		api.POST("/auth/login", authHandler.Login)
		api.POST("/auth/register", authHandler.Register)
		api.POST("/users", userHandler.CreateUser) // Registration endpoint

		// Protected routes group
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware())
		{
			// User routes
			protected.GET("/users/:id", userHandler.GetUser)

			// Auth routes
			protected.GET("/auth/session", authHandler.Session)
			protected.POST("/auth/logout", authHandler.Logout)

			// Goals routes
			protected.GET("/goals", goalHandler.List)
			protected.GET("/goals/:id", goalHandler.Get)
			protected.POST("/goals", goalHandler.Create)
			protected.PATCH("/goals/:id", goalHandler.Update)
			protected.DELETE("/goals/:id", goalHandler.Delete)

			// Holdings routes
			protected.POST("/holdings", holdingHandler.Create)
			protected.GET("/holdings", holdingHandler.List)
			protected.GET("/holdings/:id", holdingHandler.Get)
			protected.PATCH("/holdings/:id", holdingHandler.Update)
			protected.DELETE("/holdings/:id", holdingHandler.Delete)

			// Currency rates routes
			protected.GET("/currency-rates", currencyRateHandler.List)
			protected.GET("/currency-rates/:id", currencyRateHandler.Get)
			protected.PATCH("/currency-rates/:id", currencyRateHandler.Update)

			// Reports routes
			protected.POST("/reports", reportHandler.Create)
			protected.GET("/reports", reportHandler.List)
			protected.GET("/reports/:id", reportHandler.Get)
			protected.PATCH("/reports/:id", reportHandler.Update)
			protected.DELETE("/reports/:id", reportHandler.Delete)
		}
	}

	return router
}

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Initialize JWT keys
	config.InitializeJWTKeys(&config.JWTConfig{
		PrivateKeyPath: cfg.JWTPrivateKeyPath,
		PublicKeyPath:  cfg.JWTPublicKeyPath,
	})

	// Initialize session store
	sessionStore, err := session.NewSessionStore(cfg.GetRedisURL())
	if err != nil {
		log.Fatalf("Failed to initialize session store: %v", err)
	}

	// Initialize database
	db := initDB(cfg)

	// Setup router
	router := setupRouter(db, cfg)

	// Add session middleware
	router.Use(middleware.SessionMiddleware(sessionStore))

	// Start server
	serverAddr := fmt.Sprintf(":%s", cfg.APIPort)
	log.Printf("Server starting on port %s...", cfg.APIPort)
	if err := router.Run(serverAddr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

