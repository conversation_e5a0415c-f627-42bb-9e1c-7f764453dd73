FROM golang:1.24-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git

WORKDIR /app

# Enable Go's build optimizations and CGO
ENV CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GO111MODULE=on

# Cache go modules
COPY go.mod ./

# Download and tidy dependencies
RUN go mod download && \
    go mod tidy && \
    go mod verify

# Copy entire source code
COPY . .

# Run go mod tidy again after copying all source code
RUN go mod tidy

# Build with cache mounting for faster builds
RUN go build \
    -ldflags="-w -s -extldflags '-static'" \
    -a -trimpath \
    -o /app/main

# Final stage
FROM alpine:latest AS runner

WORKDIR /app

# Install required packages
RUN apk add --no-cache curl bash

# Install migrate tool
RUN curl -L https://github.com/golang-migrate/migrate/releases/download/v4.15.2/migrate.linux-amd64.tar.gz | tar xvz
RUN mv migrate /usr/local/bin/migrate
RUN chmod +x /usr/local/bin/migrate

# Copy the binary and scripts
COPY --from=builder /app/main /app/main

# Copy other files
COPY --from=builder /app/keys keys
COPY --from=builder /app/db/migrations db/migrations

# Make sure the binary is executable
RUN chmod +x /app/main && \
    ls -la /app/main && \
    # Add runtime dependencies if needed
    apk add --no-cache libc6-compat

# Create entrypoint script
RUN echo '#!/bin/sh' > /entrypoint.sh && \
    echo 'set -e' >> /entrypoint.sh && \
    echo 'echo "Running migrations..."' >> /entrypoint.sh && \
    echo 'migrate -path db/migrations -database "mysql://${DB_USER}:${DB_PASSWORD}@tcp(mysql:3306)/${DB_NAME}" up' >> /entrypoint.sh && \
    echo 'echo "Starting application..."' >> /entrypoint.sh && \
    echo 'ls -la /app && echo "Running main..." && exec /app/main' >> /entrypoint.sh && \
    chmod +x /entrypoint.sh

# Expose the API port
EXPOSE ${API_PORT}

# Run the start script
CMD ["/entrypoint.sh"]