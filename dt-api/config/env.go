package config

import (
	"fmt"
	"os"
)

type Config struct {
	JWTPrivateKeyPath string
	JWTPublicKeyPath  string
	DBHost     string
	DBPort     string
	DBUser     string
	DBPassword string
	DBName     string
	APIPort    string
	RedisHost     string
	RedisPort     string
	RedisUser     string
	RedisPassword string
	RedisDB       string
	APIPrefix    string
}

func LoadConfig() *Config {
	config := &Config{
		JWTPrivateKeyPath: getEnv("JWT_PRIVATE_KEY_PATH", "keys/private.pem"),
		JWTPublicKeyPath:  getEnv("JWT_PUBLIC_KEY_PATH", "keys/public.pem"),
		DBHost:     getEnv("DB_HOST", "mysql"),
		DBPort:     getEnv("DB_PORT", "3306"),
		DBUser:     getEnv("DB_USER", "root"),
		DBPassword: getEnv("DB_PASSWORD", "password"),
		DBName:     getEnv("DB_NAME", "api_db"),
		APIPort:    getEnv("API_PORT", "5002"),
		RedisHost:     getEnv("REDIS_HOST", "redis"),
		RedisPort:     getEnv("REDIS_PORT", "6379"),
		RedisUser:     getEnv("REDIS_USER", "default"),
		RedisPassword: getEnv("REDIS_PASSWORD", ""),
		RedisDB:       getEnv("REDIS_DB", "0"),
		APIPrefix:    getEnv("API_PREFIX", "/api/v1"),
	}

	return config
}

func (c *Config) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		c.DBUser,
		c.DBPassword,
		c.DBHost,
		c.DBPort,
		c.DBName,
	)
}

func (c *Config) GetRedisURL() string {
	auth := c.RedisUser
	if c.RedisPassword != "" {
		auth = auth + ":" + c.RedisPassword
	}
	if auth != "" {
		auth = auth + "@"
	}
	return fmt.Sprintf("redis://%s%s:%s/%s",
		auth,
		c.RedisHost,
		c.RedisPort,
		c.RedisDB,
	)
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
} 