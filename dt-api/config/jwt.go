package config

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"log"
	"os"
	"time"
)

type JWTConfig struct {
	PrivateKeyPath string
	PublicKeyPath  string
}

var (
	PrivateKey *rsa.PrivateKey
	PublicKey  *rsa.PublicKey
)

const (
	TokenExpiration    = time.Hour * 24 * 30  // 30 days
	RefreshExpiration  = time.Hour * 24 * 60  // 60 days (giving refresh token a longer life)
)

func InitializeJWTKeys(config *JWTConfig) {
	// Load private key
	privateKeyBytes, err := os.ReadFile(config.PrivateKeyPath)
	if err != nil {
		log.Fatalf("Failed to read private key: %v", err)
	}

	privateKeyBlock, _ := pem.Decode(privateKeyBytes)
	if privateKeyBlock == nil {
		log.Fatal("Failed to parse PEM block containing private key")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		log.Fatalf("Failed to parse private key: %v", err)
	}
	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		log.Fatal("Failed to convert parsed key to RSA private key")
	}
	PrivateKey = rsaPrivateKey

	// Load public key
	publicKeyBytes, err := os.ReadFile(config.PublicKeyPath)
	if err != nil {
		log.Fatalf("Failed to read public key: %v", err)
	}

	publicKeyBlock, _ := pem.Decode(publicKeyBytes)
	if publicKeyBlock == nil {
		log.Fatal("Failed to parse PEM block containing public key")
	}

	parsedPublicKey, err := x509.ParsePKIXPublicKey(publicKeyBlock.Bytes)
	if err != nil {
		log.Fatalf("Failed to parse public key: %v", err)
	}

	publicKey, ok := parsedPublicKey.(*rsa.PublicKey)
	if !ok {
		log.Fatal("Failed to convert parsed key to RSA public key")
	}
	PublicKey = publicKey
} 