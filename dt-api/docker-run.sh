#!/bin/bash

# Run setup
./scripts/setup.sh

# Generate JWT keys if they don't exist
if [ ! -f "keys/private.pem" ] || [ ! -f "keys/public.pem" ]; then
    echo "Generating JWT keys..."
    ./scripts/generate_keys.sh
fi

# Build and start the containers
docker-compose up --build -d

echo "Application is starting..."
echo "API will be available at http://localhost:${API_PORT}"
echo "To view logs: docker-compose logs -f api" 