package handlers

import (
	"Api/config"
	"Api/pkg/session"
	"Api/models"
	"fmt"
	"net/http"
	"time"
	"log"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"github.com/google/uuid"
)

type AuthHandler struct {
	db           *gorm.DB
	sessionStore *session.SessionStore
}

func NewAuthHandler(db *gorm.DB, sessionStore *session.SessionStore) *AuthHandler {
	return &AuthHandler{
		db:           db,
		sessionStore: sessionStore,
	}
}

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Pass    string `json:"pass" binding:"required"`
}

type RegisterRequest struct {
	Email string `json:"email" binding:"required,email"`
	Pass  string `json:"pass" binding:"required"`
}

// <PERSON>gin handles user authentication
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var user models.User
	if err := h.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.Pass), []byte(req.Pass)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Create session
	session, err := h.sessionStore.CreateSession(user.ID, 24*time.Hour)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create session"})
		return
	}

	// Set session cookie
	c.SetCookie(
		"session_token",
		session.ID,
		int(24*time.Hour.Seconds()),
		"/",
		"",
		true,  // Secure
		true,  // HttpOnly
	)

	// Generate access token
	accessToken, err := generateAccessToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// Get currency rates
	var currencyRates []models.CurrencyRate
	if err := h.db.Find(&currencyRates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch currency rates"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"token":         accessToken,
		"tokenType":    "Bearer",
		"user": gin.H{
			"id":        user.ID,
			"email":     user.Email,
			"license":   user.License,
			"dtCreated": user.DtCreated,
			"dtUpdated": user.DtUpdated,
			"currency":  user.Currency,
		},
		"currencyRates": currencyRates,
	})
}

func generateAccessToken(user models.User) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"sub":      user.ID.String(),
		"email":    user.Email,
		"license":  user.License,
		"exp":      now.Add(config.TokenExpiration).Unix(),
		"iat":      now.Unix(),
		"currency": user.Currency,
		"type":     "access",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	signedToken, err := token.SignedString(config.PrivateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %v", err)
	}

	return signedToken, nil
}

// Logout invalidates the user's refresh token
func (h *AuthHandler) Logout(c *gin.Context) {
	sessionID, err := c.Cookie("session_token")
	if err == nil {
		h.sessionStore.DeleteSession(sessionID)
	}
	
	c.SetCookie("session_token", "", -1, "/", "", true, true)  // HttpOnly
	c.JSON(http.StatusOK, gin.H{"message": "Logged out successfully"})
}

func (h *AuthHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user exists
	var existingUser models.User
	if err := h.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"error": gin.H{
				"message": "",
				"code":    "auth/email-taken",
			},
		})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Pass), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process password"})
		return
	}

	// Create new user
	newUser := models.User{
		Email:    req.Email,
		Pass:     string(hashedPassword),
		Currency: 1, // Default currency
	}

	if err := h.db.Create(&newUser).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// Fetch currency rates
	var currencyRates []models.CurrencyRate
	if err := h.db.Find(&currencyRates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch currency rates"})
		return
	}

	// Generate tokens
	accessToken, err := generateAccessToken(newUser)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// Create session
	session, err := h.sessionStore.CreateSession(newUser.ID, 24*time.Hour)
	if err != nil {
		log.Printf("Failed to create session: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create session"})
		return
	}

	// Set session cookie
	c.SetCookie(
		"session_token",
		session.ID,
		int(24*time.Hour.Seconds()),
		"/",
		"",
		true,  // Secure
		true,  // HttpOnly
	)

	c.JSON(http.StatusOK, gin.H{
		"token":         accessToken,
		"tokenType":    "Bearer",
		"user":         newUser,
		"currencyRates": currencyRates,
	})
}

// Session validates the access token and returns user data
func (h *AuthHandler) Session(c *gin.Context) {
	// Get access token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authentication"})
		return
	}

	// Parse Bearer token
	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authentication"})
		return
	}
	accessToken := parts[1]

	// Parse and validate access token
	claims := jwt.MapClaims{}
	token, err := jwt.ParseWithClaims(accessToken, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("invalid token")
		}
		return config.PublicKey, nil
	})

	if err != nil || !token.Valid {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authentication"})
		return
	}

	// Get user from database
	var user models.User
	userID, err := uuid.Parse(claims["sub"].(string))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authentication"})
		return
	}

	if err := h.db.First(&user, "id = ?", userID).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authentication"})
		return
	}

	// Get currency rates
	var currencyRates []models.CurrencyRate
	if err := h.db.Find(&currencyRates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch currency rates"})
		return
	}

	// Return user data and new access token
	c.JSON(http.StatusOK, gin.H{
		"token":      accessToken,  // Return the same token if it's still valid
		"tokenType": "Bearer",
		"user": gin.H{
			"id":        user.ID,
			"email":     user.Email,
			"license":   user.License,
			"dtCreated": user.DtCreated,
			"dtUpdated": user.DtUpdated,
			"currency":  user.Currency,
		},
		"currencyRates": currencyRates,
	})
} 