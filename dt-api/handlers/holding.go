package handlers

import (
	"Api/models"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type HoldingHandler struct {
	db *gorm.DB
}

func NewHoldingHandler(db *gorm.DB) *HoldingHandler {
	return &HoldingHandler{db: db}
}

func (h *HoldingHandler) Create(c *gin.Context) {
	var holding models.Holding
	if err := c.ShouldBindJSON(&holding); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	claims, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authenticated"})
		return
	}
	userClaims := claims.(jwt.MapClaims)
	userID, err := uuid.Parse(userClaims["sub"].(string))
	if err != nil {
		c.J<PERSON>N(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}
	holding.Uid = userID

	if err := h.db.Create(&holding).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create holding"})
		return
	}

	c.JSON(http.StatusCreated, holding)
}

func (h *HoldingHandler) List(c *gin.Context) {
	// Get user ID from context
	claims, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authenticated"})
		return
	}
	userClaims := claims.(jwt.MapClaims)
	userID, err := uuid.Parse(userClaims["sub"].(string))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	var holdings []models.Holding
	if err := h.db.Where("uid = ?", userID).Find(&holdings).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list holdings"})
		return
	}

	c.JSON(http.StatusOK, holdings)
}

func (h *HoldingHandler) Get(c *gin.Context) {
	id, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid holding ID"})
		return
	}

	// Get user ID from context
	claims, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authenticated"})
		return
	}
	userClaims := claims.(jwt.MapClaims)
	userID, err := uuid.Parse(userClaims["sub"].(string))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	var holding models.Holding
	if err := h.db.Where("id = ? AND uid = ?", id, userID).First(&holding).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Holding not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch holding"})
		return
	}

	c.JSON(http.StatusOK, holding)
}

func (h *HoldingHandler) Update(c *gin.Context) {
	id, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid holding ID"})
		return
	}

	// Get user ID from context
	claims, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authenticated"})
		return
	}
	userClaims := claims.(jwt.MapClaims)
	userID, err := uuid.Parse(userClaims["sub"].(string))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	var holding models.Holding
	if err := h.db.Where("id = ? AND uid = ?", id, userID).First(&holding).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Holding not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch holding"})
		return
	}

	if err := c.ShouldBindJSON(&holding); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Ensure user can't change the ownership
	holding.Uid = userID

	if err := h.db.Save(&holding).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update holding"})
		return
	}

	c.JSON(http.StatusOK, holding)
}

func (h *HoldingHandler) Delete(c *gin.Context) {
	id, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid holding ID"})
		return
	}

	// Get user ID from context
	claims, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authenticated"})
		return
	}
	userClaims := claims.(jwt.MapClaims)
	userID, err := uuid.Parse(userClaims["sub"].(string))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	result := h.db.Where("id = ? AND uid = ?", id, userID).Delete(&models.Holding{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete holding"})
		return
	}
	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Holding not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Holding deleted successfully"})
} 