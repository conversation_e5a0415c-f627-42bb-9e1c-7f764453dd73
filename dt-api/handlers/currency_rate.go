package handlers

import (
	"Api/models"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type CurrencyRateHandler struct {
	db *gorm.DB
}

func NewCurrencyRateHandler(db *gorm.DB) *CurrencyRateHandler {
	return &CurrencyRateHandler{db: db}
}

func (h *CurrencyRateHandler) List(c *gin.Context) {
	var rates []models.CurrencyRate
	if err := h.db.Find(&rates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list currency rates"})
		return
	}

	c.JSO<PERSON>(http.StatusOK, rates)
}

func (h *CurrencyRateHandler) Get(c *gin.Context) {
	var rate models.CurrencyRate
	if err := h.db.First(&rate, "id = ?", c<PERSON>("id")).Error; err != nil {
		c.J<PERSON>(http.StatusNotFound, gin.H{"error": "Currency rate not found"})
		return
	}

	c.<PERSON>(http.StatusOK, rate)
}

func (h *CurrencyRateHandler) Update(c *gin.Context) {
	var rate models.CurrencyRate
	if err := h.db.First(&rate, "id = ?", c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Currency rate not found"})
		return
	}

	if err := c.ShouldBindJSON(&rate); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.Save(&rate).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update currency rate"})
		return
	}

	c.JSON(http.StatusOK, rate)
}

type CurrencyRateResponse struct {
	ID            int8            `json:"id"`
	Code          string          `json:"code"`
	Rate          decimal.Decimal `json:"rate"`
	DtUpdated     time.Time       `json:"dtUpdated"`
} 